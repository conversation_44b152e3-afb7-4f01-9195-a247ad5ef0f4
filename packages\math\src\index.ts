/**
 * Simple math utilities for the Echo platform
 */

export function add(a: number, b: number): number {
  return a + b;
}

export function subtract(a: number, b: number): number {
  return a - b;
}

export function multiply(a: number, b: number): number {
  return a * b;
}

export function divide(a: number, b: number): number {
  if (b === 0) {
    throw new Error('Division by zero is not allowed');
  }
  return a / b;
}

export function percentage(value: number, total: number): number {
  if (total === 0) {
    return 0;
  }
  return (value / total) * 100;
}

// Export types
export interface MathOperation {
  operation: 'add' | 'subtract' | 'multiply' | 'divide';
  operands: [number, number];
  result: number;
}
