export default function Widget() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-md w-full">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Echo Support Widget
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Customer-facing chat widget for real-time support
          </p>
          
          <div className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Features Coming Soon:
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• AI-powered chat responses</li>
                <li>• Voice support integration</li>
                <li>• Session-based authentication</li>
                <li>• Multi-tenant support</li>
              </ul>
            </div>
            
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              Start Chat (Coming Soon)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
