# Echo - AI-Powered Customer Support Platform

A multi-tenant, AI-powered customer support platform built with modern web technologies.

## Architecture

This is a monorepo built with Turborepo containing:

### Applications
- **`apps/web`** - Main dashboard application (Next.js 15 + React 19)
- **`apps/widget`** - Customer-facing chat widget (Next.js 15 + React 19)

### Packages
- **`packages/ui`** - Shared UI components with Tailwind CSS
- **`packages/math`** - Shared math utilities (demo package)

## Tech Stack

- **Framework**: Next.js 15 with React 19
- **Styling**: Tailwind CSS 4
- **Package Manager**: PNPM v10
- **Build System**: Turborepo
- **Language**: TypeScript
- **Backend**: Convex (coming soon)
- **Auth**: Clerk (coming soon)
- **AI**: Convex AI Agents (coming soon)
- **Voice**: Vapi (coming soon)

## Getting Started

### Prerequisites

- Node.js 18+ 
- PNPM 10+

### Installation

1. Install dependencies:
```bash
pnpm install
```

2. Start development servers:
```bash
pnpm dev
```

This will start:
- Web app at http://localhost:3000
- Widget app at http://localhost:3001

### Available Scripts

- `pnpm dev` - Start all apps in development mode
- `pnpm build` - Build all apps for production
- `pnpm lint` - Lint all packages
- `pnpm type-check` - Type check all packages
- `pnpm clean` - Clean all build artifacts

## Development Roadmap

### Phase 1: Foundations ✅
- [x] Turborepo setup with PNPM
- [x] Next.js 15 + React 19 apps
- [x] Shared UI package
- [x] Basic project structure

### Phase 2: Backend & Auth (Next)
- [ ] Convex backend integration
- [ ] Clerk authentication
- [ ] Multi-tenant organizations
- [ ] Database schema

### Phase 3: Core Features
- [ ] AI chat agents
- [ ] Voice integration with Vapi
- [ ] Dashboard UI
- [ ] Widget UI

### Phase 4: Production
- [ ] Error tracking with Sentry
- [ ] Deployment setup
- [ ] Monitoring & analytics

## Project Structure

```
echo/
├── apps/
│   ├── web/          # Main dashboard app
│   └── widget/       # Customer chat widget
├── packages/
│   ├── ui/           # Shared UI components
│   └── math/         # Shared utilities
├── package.json      # Root package.json
├── turbo.json        # Turborepo configuration
└── pnpm-workspace.yaml
```

## Contributing

This project follows the development guide outlined in the Echo documentation. Please refer to the complete guide for detailed implementation steps.

## License

Private - All rights reserved
