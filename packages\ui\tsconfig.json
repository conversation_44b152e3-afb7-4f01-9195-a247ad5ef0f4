{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}